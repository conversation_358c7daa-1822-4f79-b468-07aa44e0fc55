<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="
           http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context.xsd
           http://www.springframework.org/schema/mvc
           http://www.springframework.org/schema/mvc/spring-mvc.xsd">

    <!-- 启用注解驱动 -->
    <mvc:annotation-driven />
    
    <!-- 扫描控制器包 -->
    <context:component-scan base-package="com.springmvc.student" />
    
    <!-- 配置视图解析器 -->
    <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="prefix" value="/WEB-INF/studentpages/" />
        <property name="suffix" value=".jsp" />
    </bean>
    
    <!-- 配置视图控制器，指定主页面的映射路径 -->
    <mvc:view-controller path="/index" view-name="login" />
    
    <!-- 处理静态资源 -->
    <mvc:default-servlet-handler />
    
</beans>
