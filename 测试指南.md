# 学生管理系统测试指南

## 测试环境要求
- Java 8 或更高版本
- Apache Tomcat 9 或更高版本
- 现代浏览器（Chrome、Firefox、Edge等）

## 部署步骤

### 1. 使用IDE部署（推荐）
1. 将项目导入Eclipse或IntelliJ IDEA
2. 配置Tomcat服务器
3. 右键项目 → Run As → Run on Server
4. 选择Tomcat服务器并启动

### 2. 手动部署到Tomcat
1. 将整个项目复制到Tomcat的webapps目录下
2. 重命名项目文件夹为 `student-management`
3. 启动Tomcat服务器
4. 等待项目自动部署

## 功能测试

### 测试1：访问登录页面
1. **操作**：在浏览器中访问 `http://localhost:8080/student-management/login`
2. **预期结果**：
   - 显示登录页面
   - 页面标题为"学生管理系统 - 登录"
   - 包含账号和密码输入框
   - 包含登录和重置按钮
   - 显示测试账号信息

### 测试2：错误登录验证
1. **操作**：输入错误的用户名和密码（如：test/test）
2. **点击**：登录按钮
3. **预期结果**：
   - 页面刷新但仍停留在登录页面
   - 显示红色错误提示："用户名或密码错误，请重新输入！"
   - 输入框内容被清空

### 测试3：正确登录验证
1. **操作**：输入正确的测试账号
   - 账号：`admin`
   - 密码：`123456`
2. **点击**：登录按钮
3. **预期结果**：
   - 页面跳转到主页面
   - URL变为：`http://localhost:8080/student-management/main`
   - 显示欢迎信息和用户信息

### 测试4：主页面功能验证
1. **检查用户信息显示**：
   - 账号：admin
   - 身份：管理员
2. **检查页面元素**：
   - 页面标题为"学生管理系统 - 主页"
   - 显示当前时间（每秒更新）
   - 包含"退出登录"按钮

### 测试5：退出登录功能
1. **操作**：点击"退出登录"链接
2. **预期结果**：
   - 弹出确认对话框："确定要退出登录吗？"
   - 点击确定后跳转到登录页面
   - 用户会话被清除

### 测试6：会话管理验证
1. **操作**：登录成功后，直接在地址栏访问 `http://localhost:8080/student-management/main`
2. **预期结果**：正常显示主页面（因为用户已登录）

3. **操作**：退出登录后，再次直接访问主页面URL
4. **预期结果**：自动重定向到登录页面（因为用户未登录）

### 测试7：重置按钮功能
1. **操作**：在登录页面输入一些内容
2. **点击**：重置按钮
3. **预期结果**：所有输入框内容被清空

## 常见问题排查

### 问题1：404错误
- **原因**：项目未正确部署或URL路径错误
- **解决**：检查Tomcat是否启动，项目是否正确部署

### 问题2：500错误
- **原因**：Spring配置错误或缺少依赖jar包
- **解决**：检查web.xml和student.xml配置，确保Spring MVC依赖完整

### 问题3：页面显示乱码
- **原因**：字符编码设置问题
- **解决**：确保所有文件都使用UTF-8编码

### 问题4：CSS样式不显示
- **原因**：静态资源访问问题
- **解决**：检查Spring MVC配置中的静态资源处理

## 测试数据
```
测试账号：admin
测试密码：123456
用户身份：管理员
```

## 预期的页面流程
```
访问首页 → 登录页面 → 输入账号密码 → 验证成功 → 主页面 → 退出登录 → 登录页面
```

## 成功标准
- ✅ 所有页面正常显示
- ✅ 登录验证功能正常
- ✅ 错误提示正确显示
- ✅ 会话管理正常工作
- ✅ 页面跳转逻辑正确
- ✅ 用户信息正确显示
- ✅ 退出登录功能正常

## 注意事项
1. 确保Tomcat端口（默认8080）未被占用
2. 如果修改了代码，需要重新部署项目
3. 浏览器可能会缓存页面，测试时建议使用无痕模式或清除缓存
4. 确保项目路径中没有中文字符，避免编码问题
