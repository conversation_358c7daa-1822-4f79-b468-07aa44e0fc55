# 学生管理系统 - Spring MVC登录功能

## 项目概述
这是一个基于Spring MVC框架开发的简单学生管理系统登录功能，实现了用户登录验证、主页显示和退出登录功能。

## 项目结构
```
demo8/
├── pom.xml                                    # Maven项目配置文件
├── src/
│   └── main/
│       ├── java/
│       │   └── com/
│       │       └── springmvc/
│       │           └── student/
│       │               ├── User.java         # 用户实体类
│       │               └── UserController.java # 用户控制器类
│       └── webapp/
│           └── WEB-INF/
│               ├── web.xml                   # Web应用配置文件
│               ├── student.xml               # Spring MVC配置文件
│               └── studentpages/
│                   ├── login.jsp             # 登录页面
│                   └── main.jsp              # 主页面
└── README.md                                 # 项目说明文档
```

## 功能特性
1. **用户登录验证**：验证用户输入的账号和密码
2. **错误提示**：当用户信息错误时显示提示信息
3. **主页显示**：登录成功后显示用户信息
4. **退出登录**：清除用户会话信息并返回登录页面
5. **会话管理**：使用HttpSession管理用户登录状态

## 测试账号
- **账号**：admin
- **密码**：123456
- **身份**：管理员

## 部署步骤

### 方法一：使用Maven（推荐）
1. 确保已安装Java 8+和Maven 3.6+
2. 在项目根目录执行：
   ```bash
   mvn clean compile
   mvn clean package
   ```
3. 将生成的war包部署到Tomcat服务器

### 方法二：手动部署
1. 确保已安装Java 8+和Tomcat 9+
2. 手动编译Java文件：
   ```bash
   # 创建classes目录
   mkdir -p target/classes
   
   # 编译Java文件（需要Spring MVC相关jar包在classpath中）
   javac -cp "lib/*" -d target/classes src/main/java/com/springmvc/student/*.java
   ```
3. 创建WAR包结构并部署到Tomcat

### 方法三：IDE部署
1. 将项目导入Eclipse或IntelliJ IDEA
2. 配置Tomcat服务器
3. 直接运行项目

## 访问方式
1. 启动Tomcat服务器
2. 在浏览器中访问：`http://localhost:8080/student-management/login`
3. 使用测试账号登录系统

## 技术栈
- **后端框架**：Spring MVC 5.3.21
- **视图技术**：JSP + JSTL
- **服务器**：Apache Tomcat 9+
- **构建工具**：Maven 3.6+
- **Java版本**：Java 8+

## 主要文件说明

### 1. User.java
用户实体类，包含账号、密码和身份属性，以及对应的getter和setter方法。

### 2. UserController.java
用户控制器类，处理登录验证和退出登录的业务逻辑：
- `showLogin()`：显示登录页面
- `login()`：处理登录验证
- `showMain()`：显示主页面
- `logout()`：处理退出登录

### 3. web.xml
Web应用配置文件，配置了：
- Spring MVC的DispatcherServlet
- 字符编码过滤器
- 配置文件路径指定为student.xml

### 4. student.xml
Spring MVC配置文件，配置了：
- 注解驱动
- 组件扫描
- 视图解析器
- 视图控制器（主页面映射路径）

### 5. login.jsp
登录页面，包含：
- 用户信息输入表单
- 错误信息显示
- 登录和重置按钮
- 响应式CSS样式

### 6. main.jsp
主页面，包含：
- 用户信息显示
- 退出登录链接
- 实时时间显示
- 美观的页面布局

## 实验要求对应
本项目完全按照实验要求实现：

1. ✅ 使用Spring MVC创建登录页面
2. ✅ 实现登录功能验证
3. ✅ 错误信息提示功能
4. ✅ 登录成功跳转到主页面
5. ✅ 主页面显示用户信息
6. ✅ 退出登录功能
7. ✅ 在com.springmvc.student包中创建用户类
8. ✅ 在com.springmvc.student包中创建控制器类
9. ✅ 在studentpages文件夹中创建登录页面
10. ✅ 在studentpages文件夹中创建主页面
11. ✅ 在student.xml中配置视图控制器
12. ✅ 在web.xml中修改Spring MVC配置文件名称

## 注意事项
1. 确保Tomcat服务器正常运行
2. 检查端口号是否被占用
3. 确保所有依赖jar包都在classpath中
4. 注意字符编码设置为UTF-8
