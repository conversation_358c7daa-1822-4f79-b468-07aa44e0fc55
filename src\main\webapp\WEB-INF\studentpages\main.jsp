<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>学生管理系统 - 主页</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
        }
        .container {
            max-width: 800px;
            margin: 40px auto;
            padding: 0 20px;
        }
        .welcome-card {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        .welcome-title {
            color: #333;
            margin-bottom: 20px;
            font-size: 24px;
            text-align: center;
        }
        .user-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .user-info h3 {
            margin-top: 0;
            color: #333;
            font-size: 18px;
        }
        .info-item {
            margin: 10px 0;
            font-size: 16px;
        }
        .info-label {
            font-weight: bold;
            color: #555;
            display: inline-block;
            width: 80px;
        }
        .info-value {
            color: #333;
        }
        .action-section {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .action-title {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
        }
        .logout-link {
            display: inline-block;
            padding: 12px 30px;
            background-color: #dc3545;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .logout-link:hover {
            background-color: #c82333;
            text-decoration: none;
            color: white;
        }
        .system-time {
            text-align: center;
            color: #6c757d;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
    <script>
        // 显示当前时间
        function updateTime() {
            var now = new Date();
            var timeString = now.toLocaleString('zh-CN');
            document.getElementById('currentTime').innerHTML = '当前时间：' + timeString;
        }
        
        // 页面加载完成后开始更新时间
        window.onload = function() {
            updateTime();
            setInterval(updateTime, 1000); // 每秒更新一次
        }
        
        // 退出登录确认
        function confirmLogout() {
            return confirm('确定要退出登录吗？');
        }
    </script>
</head>
<body>
    <!-- 页面头部 -->
    <div class="header">
        <h1>学生管理系统</h1>
    </div>
    
    <div class="container">
        <!-- 欢迎信息卡片 -->
        <div class="welcome-card">
            <h2 class="welcome-title">欢迎进入学生管理系统</h2>
            
            <!-- 用户信息显示 -->
            <div class="user-info">
                <h3>当前登录用户信息</h3>
                <div class="info-item">
                    <span class="info-label">账号：</span>
                    <span class="info-value">${user.account}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">身份：</span>
                    <span class="info-value">${user.identity}</span>
                </div>
            </div>
        </div>
        
        <!-- 操作区域 -->
        <div class="action-section">
            <h3 class="action-title">系统操作</h3>
            <p>您已成功登录系统，可以进行相关操作。</p>
            
            <!-- 退出登录链接 -->
            <a href="${pageContext.request.contextPath}/logout" 
               class="logout-link" 
               onclick="return confirmLogout()">
                退出登录
            </a>
        </div>
        
        <!-- 系统时间显示 -->
        <div class="system-time" id="currentTime">
            <!-- 时间将通过JavaScript动态更新 -->
        </div>
    </div>
</body>
</html>
