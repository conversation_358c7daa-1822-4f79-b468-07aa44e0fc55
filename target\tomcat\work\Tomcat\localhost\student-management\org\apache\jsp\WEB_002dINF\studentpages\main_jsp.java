/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-05-30 15:43:54 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.studentpages;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;

public final class main_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html;charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html>\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <title>学生管理系统 - 主页</title>\n");
      out.write("    <style>\n");
      out.write("        body {\n");
      out.write("            font-family: Arial, sans-serif;\n");
      out.write("            background-color: #f5f5f5;\n");
      out.write("            margin: 0;\n");
      out.write("            padding: 0;\n");
      out.write("        }\n");
      out.write("        .header {\n");
      out.write("            background-color: #007bff;\n");
      out.write("            color: white;\n");
      out.write("            padding: 20px 0;\n");
      out.write("            text-align: center;\n");
      out.write("            box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n");
      out.write("        }\n");
      out.write("        .header h1 {\n");
      out.write("            margin: 0;\n");
      out.write("            font-size: 28px;\n");
      out.write("        }\n");
      out.write("        .container {\n");
      out.write("            max-width: 800px;\n");
      out.write("            margin: 40px auto;\n");
      out.write("            padding: 0 20px;\n");
      out.write("        }\n");
      out.write("        .welcome-card {\n");
      out.write("            background-color: white;\n");
      out.write("            padding: 30px;\n");
      out.write("            border-radius: 10px;\n");
      out.write("            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n");
      out.write("            margin-bottom: 30px;\n");
      out.write("        }\n");
      out.write("        .welcome-title {\n");
      out.write("            color: #333;\n");
      out.write("            margin-bottom: 20px;\n");
      out.write("            font-size: 24px;\n");
      out.write("            text-align: center;\n");
      out.write("        }\n");
      out.write("        .user-info {\n");
      out.write("            background-color: #f8f9fa;\n");
      out.write("            padding: 20px;\n");
      out.write("            border-radius: 8px;\n");
      out.write("            border-left: 4px solid #007bff;\n");
      out.write("        }\n");
      out.write("        .user-info h3 {\n");
      out.write("            margin-top: 0;\n");
      out.write("            color: #333;\n");
      out.write("            font-size: 18px;\n");
      out.write("        }\n");
      out.write("        .info-item {\n");
      out.write("            margin: 10px 0;\n");
      out.write("            font-size: 16px;\n");
      out.write("        }\n");
      out.write("        .info-label {\n");
      out.write("            font-weight: bold;\n");
      out.write("            color: #555;\n");
      out.write("            display: inline-block;\n");
      out.write("            width: 80px;\n");
      out.write("        }\n");
      out.write("        .info-value {\n");
      out.write("            color: #333;\n");
      out.write("        }\n");
      out.write("        .action-section {\n");
      out.write("            background-color: white;\n");
      out.write("            padding: 30px;\n");
      out.write("            border-radius: 10px;\n");
      out.write("            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n");
      out.write("            text-align: center;\n");
      out.write("        }\n");
      out.write("        .action-title {\n");
      out.write("            color: #333;\n");
      out.write("            margin-bottom: 20px;\n");
      out.write("            font-size: 20px;\n");
      out.write("        }\n");
      out.write("        .logout-link {\n");
      out.write("            display: inline-block;\n");
      out.write("            padding: 12px 30px;\n");
      out.write("            background-color: #dc3545;\n");
      out.write("            color: white;\n");
      out.write("            text-decoration: none;\n");
      out.write("            border-radius: 5px;\n");
      out.write("            font-size: 16px;\n");
      out.write("            transition: background-color 0.3s;\n");
      out.write("        }\n");
      out.write("        .logout-link:hover {\n");
      out.write("            background-color: #c82333;\n");
      out.write("            text-decoration: none;\n");
      out.write("            color: white;\n");
      out.write("        }\n");
      out.write("        .system-time {\n");
      out.write("            text-align: center;\n");
      out.write("            color: #6c757d;\n");
      out.write("            margin-top: 20px;\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("    </style>\n");
      out.write("    <script>\n");
      out.write("        // 显示当前时间\n");
      out.write("        function updateTime() {\n");
      out.write("            var now = new Date();\n");
      out.write("            var timeString = now.toLocaleString('zh-CN');\n");
      out.write("            document.getElementById('currentTime').innerHTML = '当前时间：' + timeString;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        // 页面加载完成后开始更新时间\n");
      out.write("        window.onload = function() {\n");
      out.write("            updateTime();\n");
      out.write("            setInterval(updateTime, 1000); // 每秒更新一次\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        // 退出登录确认\n");
      out.write("        function confirmLogout() {\n");
      out.write("            return confirm('确定要退出登录吗？');\n");
      out.write("        }\n");
      out.write("    </script>\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <!-- 页面头部 -->\n");
      out.write("    <div class=\"header\">\n");
      out.write("        <h1>学生管理系统</h1>\n");
      out.write("    </div>\n");
      out.write("    \n");
      out.write("    <div class=\"container\">\n");
      out.write("        <!-- 欢迎信息卡片 -->\n");
      out.write("        <div class=\"welcome-card\">\n");
      out.write("            <h2 class=\"welcome-title\">欢迎进入学生管理系统</h2>\n");
      out.write("            \n");
      out.write("            <!-- 用户信息显示 -->\n");
      out.write("            <div class=\"user-info\">\n");
      out.write("                <h3>当前登录用户信息</h3>\n");
      out.write("                <div class=\"info-item\">\n");
      out.write("                    <span class=\"info-label\">账号：</span>\n");
      out.write("                    <span class=\"info-value\">");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${user.account}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("</span>\n");
      out.write("                </div>\n");
      out.write("                <div class=\"info-item\">\n");
      out.write("                    <span class=\"info-label\">身份：</span>\n");
      out.write("                    <span class=\"info-value\">");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${user.identity}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("</span>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("        \n");
      out.write("        <!-- 操作区域 -->\n");
      out.write("        <div class=\"action-section\">\n");
      out.write("            <h3 class=\"action-title\">系统操作</h3>\n");
      out.write("            <p>您已成功登录系统，可以进行相关操作。</p>\n");
      out.write("            \n");
      out.write("            <!-- 退出登录链接 -->\n");
      out.write("            <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/logout\" \n");
      out.write("               class=\"logout-link\" \n");
      out.write("               onclick=\"return confirmLogout()\">\n");
      out.write("                退出登录\n");
      out.write("            </a>\n");
      out.write("        </div>\n");
      out.write("        \n");
      out.write("        <!-- 系统时间显示 -->\n");
      out.write("        <div class=\"system-time\" id=\"currentTime\">\n");
      out.write("            <!-- 时间将通过JavaScript动态更新 -->\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
