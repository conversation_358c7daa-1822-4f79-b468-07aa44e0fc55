package com.springmvc.student;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpSession;

/**
 * 用户控制器类
 * 处理用户登录验证和退出登录功能
 */
@Controller
public class UserController {
    
    // 预设的用户信息（实际项目中应该从数据库获取）
    private static final String VALID_ACCOUNT = "admin";
    private static final String VALID_PASSWORD = "123456";
    private static final String VALID_IDENTITY = "管理员";
    
    /**
     * 显示登录页面
     */
    @RequestMapping(value = "/login", method = RequestMethod.GET)
    public String showLogin() {
        return "login";
    }
    
    /**
     * 处理登录验证
     * @param account 用户账号
     * @param password 用户密码
     * @param model 模型对象
     * @param session 会话对象
     * @return 视图名称
     */
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public String login(@RequestParam("account") String account,
                       @RequestParam("password") String password,
                       Model model,
                       HttpSession session) {
        
        // 验证用户信息
        if (VALID_ACCOUNT.equals(account) && VALID_PASSWORD.equals(password)) {
            // 用户信息正确，创建用户对象并保存到session
            User user = new User(account, password, VALID_IDENTITY);
            session.setAttribute("user", user);
            
            // 重定向到主页面
            return "redirect:/main";
        } else {
            // 用户信息错误，设置错误提示信息
            model.addAttribute("errorMessage", "用户名或密码错误，请重新输入！");
            return "login";
        }
    }
    
    /**
     * 显示主页面
     */
    @RequestMapping(value = "/main", method = RequestMethod.GET)
    public String showMain(HttpSession session, Model model) {
        // 检查用户是否已登录
        User user = (User) session.getAttribute("user");
        if (user == null) {
            // 用户未登录，重定向到登录页面
            return "redirect:/login";
        }
        
        // 将用户信息添加到模型中
        model.addAttribute("user", user);
        return "main";
    }
    
    /**
     * 退出登录
     */
    @RequestMapping(value = "/logout", method = RequestMethod.GET)
    public String logout(HttpSession session) {
        // 清除session中的用户信息
        session.removeAttribute("user");
        // 或者直接使session失效
        // session.invalidate();
        
        // 转发到登录页面
        return "forward:/login";
    }
}
