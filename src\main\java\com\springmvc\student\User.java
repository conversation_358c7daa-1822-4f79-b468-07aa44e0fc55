package com.springmvc.student;

/**
 * 用户实体类
 * 包含账号、密码和身份属性
 */
public class User {
    private String account;    // 账号
    private String password;   // 密码
    private String identity;   // 身份
    
    // 无参构造方法
    public User() {
    }
    
    // 有参构造方法
    public User(String account, String password, String identity) {
        this.account = account;
        this.password = password;
        this.identity = identity;
    }
    
    // 账号的getter和setter方法
    public String getAccount() {
        return account;
    }
    
    public void setAccount(String account) {
        this.account = account;
    }
    
    // 密码的getter和setter方法
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    // 身份的getter和setter方法
    public String getIdentity() {
        return identity;
    }
    
    public void setIdentity(String identity) {
        this.identity = identity;
    }
    
    @Override
    public String toString() {
        return "User{" +
                "account='" + account + '\'' +
                ", password='" + password + '\'' +
                ", identity='" + identity + '\'' +
                '}';
    }
}
