@echo off
echo ================================
echo 学生管理系统构建脚本
echo ================================

echo.
echo 正在检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请先安装Java 8或更高版本
    pause
    exit /b 1
)

echo.
echo 正在检查Maven环境...
mvn --version
if %errorlevel% neq 0 (
    echo 警告：未找到Maven环境，将尝试手动编译
    goto manual_build
)

echo.
echo 使用Maven构建项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 错误：Maven编译失败
    pause
    exit /b 1
)

mvn clean package
if %errorlevel% neq 0 (
    echo 错误：Maven打包失败
    pause
    exit /b 1
)

echo.
echo 构建成功！WAR包位置：target/student-management.war
echo 请将WAR包部署到Tomcat服务器的webapps目录
goto end

:manual_build
echo.
echo 开始手动编译...
if not exist "target\classes" mkdir target\classes

echo 注意：手动编译需要Spring MVC相关jar包
echo 请确保以下jar包在lib目录中：
echo - spring-webmvc-5.3.21.jar
echo - spring-context-5.3.21.jar
echo - spring-core-5.3.21.jar
echo - spring-beans-5.3.21.jar
echo - spring-web-5.3.21.jar
echo - javax.servlet-api-4.0.1.jar
echo - jstl-1.2.jar

if exist "lib\*.jar" (
    echo 正在编译Java文件...
    javac -cp "lib\*" -d target\classes src\main\java\com\springmvc\student\*.java
    if %errorlevel% neq 0 (
        echo 错误：编译失败
        pause
        exit /b 1
    )
    echo 编译成功！
) else (
    echo 错误：未找到lib目录或jar包，请手动下载Spring MVC依赖
)

:end
echo.
echo ================================
echo 构建完成
echo ================================
echo.
echo 部署说明：
echo 1. 启动Tomcat服务器
echo 2. 将项目部署到Tomcat
echo 3. 访问：http://localhost:8080/student-management/login
echo 4. 使用测试账号：admin/123456
echo.
pause
